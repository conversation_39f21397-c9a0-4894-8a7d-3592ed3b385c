<template>
  <div class="panel-content">
    <div class="panel-header">
      <div class="panel-title">
        <span class="panel-icon">🌍</span>
        <span>世界观设定</span>
      </div>
      <div class="world-actions">
        <el-button size="small" type="primary" @click="addWorldSetting">
          <el-icon><Plus /></el-icon>
          新增
        </el-button>
        <el-button size="small" type="success" @click="openWorldGenerateDialog">
          🤖 AI生成
        </el-button>
      </div>
    </div>

    <div class="worldview-list" v-loading="loading" element-loading-text="正在加载世界观...">
      <div v-for="setting in worldSettings" :key="setting.id" class="worldview-item">
        <div class="worldview-content" @click="editWorldSetting(setting)">
          <div class="worldview-header">
            <h4>{{ setting.title }}</h4>
            <el-tag :type="getWorldSettingTagType(setting.category)">{{ getWorldSettingTagText(setting.category) }}</el-tag>
          </div>
          <el-tooltip
            v-if="setting.description"
            :content="setting.description"
            placement="right"
            :disabled="setting.description.length <= 80"
            effect="light"
            :show-after="300"
          >
            <p class="worldview-description worldview-description-truncated">
              {{ setting.description.length > 80 ? setting.description.substring(0, 80) + '...' : setting.description }}
            </p>
          </el-tooltip>
          <p v-else class="worldview-description">暂无描述</p>
          <div class="worldview-meta">
            <span class="create-time">{{ formatDate(setting.createdAt) }}</span>
            <span v-if="setting.generated" class="ai-generated">AI生成</span>
          </div>
        </div>
        <div class="worldview-actions">
          <el-dropdown @command="(cmd) => handleWorldSettingAction(cmd, setting)" trigger="click">
            <el-button size="small" type="text" @click.stop>
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="edit">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-dropdown-item>
                <el-dropdown-item command="duplicate">
                  <el-icon><CopyDocument /></el-icon>
                  复制
                </el-dropdown-item>
                <el-dropdown-item command="delete" divided>
                  <el-icon><Delete /></el-icon>
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <div v-if="worldSettings.length === 0" class="empty-state">
        <p>暂无世界观设定</p>
        <el-button size="small" @click="addWorldSetting">创建第一个设定</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import { Plus, MoreFilled, Edit, CopyDocument, Delete } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  worldSettings: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'add-world-setting',
  'edit-world-setting',
  'world-setting-action',
  'open-world-generate-dialog'
])

// 方法
const addWorldSetting = () => {
  emit('add-world-setting')
}

const editWorldSetting = (setting) => {
  emit('edit-world-setting', setting)
}

const handleWorldSettingAction = (command, setting) => {
  emit('world-setting-action', command, setting)
}

const openWorldGenerateDialog = () => {
  emit('open-world-generate-dialog')
}

// 获取世界观设定标签类型
const getWorldSettingTagType = (category) => {
  const typeMap = {
    'setting': 'primary',
    'magic': 'danger',
    'politics': 'warning',
    'geography': 'success',
    'history': 'info'
  }
  return typeMap[category] || 'info'
}

// 获取世界观设定标签文本
const getWorldSettingTagText = (category) => {
  const textMap = {
    'setting': '世界设定',
    'magic': '魔法体系',
    'politics': '政治势力',
    'geography': '地理环境',
    'history': '历史背景'
  }
  return textMap[category] || category
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return ''
  const d = new Date(date)
  return d.toLocaleDateString('zh-CN', {
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
.panel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  overflow: hidden;
  height: calc(100vh - 150px);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.panel-icon {
  font-size: 18px;
}

.world-actions {
  display: flex;
  gap: 8px;
}

.worldview-list {
  flex: 1;
  overflow-y: auto;
  padding-right: 4px;
  max-height: calc(100vh - 190px);
}

.worldview-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.worldview-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
  transform: translateY(-1px);
}

.worldview-content {
  flex: 1;
  cursor: pointer;
}

.worldview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.worldview-header h4 {
  margin: 0;
  font-size: 14px;
  color: #303133;
  flex: 1;
  margin-right: 8px;
}

.worldview-description {
  margin: 6px 0;
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.worldview-description-truncated {
  cursor: help;
  transition: color 0.2s ease;
}

.worldview-description-truncated:hover {
  color: #303133;
}

.worldview-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 6px;
}

.worldview-meta .create-time {
  font-size: 12px;
  color: #909399;
}

.worldview-meta .ai-generated {
  font-size: 11px;
  color: #67c23a;
  background-color: #f0f9ff;
  padding: 2px 6px;
  border-radius: 10px;
  border: 1px solid #b3d8ff;
}

.worldview-actions {
  flex-shrink: 0;
  margin-left: 8px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style>
