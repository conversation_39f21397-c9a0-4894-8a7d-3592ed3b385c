<template>
  <div class="panel-content">
    <div class="panel-header">
      <div class="panel-title">
        <span class="panel-icon">📚</span>
        <span>语料库</span>
      </div>
      <el-button size="small" type="primary" @click="addCorpus">
        <el-icon><Plus /></el-icon>
        新增
      </el-button>
    </div>

    <div class="corpus-list" v-loading="loading" element-loading-text="正在加载语料库...">
      <div v-for="corpus in corpusData" :key="corpus.id" class="corpus-item">
        <div class="corpus-content">
          <div class="corpus-header">
            <h4>{{ corpus.title }}</h4>
            <el-tag :type="getCorpusType(corpus.type)">{{ getCorpusTypeText(corpus.type) }}</el-tag>
          </div>
          <el-tooltip
            :content="corpus.content"
            placement="right"
            :disabled="corpus.content.length <= 100"
            effect="light"
            :show-after="300"
          >
            <p class="corpus-preview corpus-preview-truncated">
              {{ corpus.content.length > 100 ? corpus.content.substring(0, 100) + '...' : corpus.content }}
            </p>
          </el-tooltip>
        </div>
        <div class="corpus-actions">
          <el-button size="small" @click="editCorpus(corpus)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteCorpus(corpus)">删除</el-button>
        </div>
      </div>

      <div v-if="corpusData.length === 0" class="empty-state">
        <p>暂无语料数据</p>
        <el-button size="small" @click="addCorpus">添加第一个语料</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import { Plus } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  corpusData: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'add-corpus',
  'edit-corpus',
  'delete-corpus'
])

// 方法
const addCorpus = () => {
  emit('add-corpus')
}

const editCorpus = (corpus) => {
  emit('edit-corpus', corpus)
}

const deleteCorpus = (corpus) => {
  emit('delete-corpus', corpus)
}

// 获取语料库类型样式
const getCorpusType = (type) => {
  const typeMap = {
    'description': 'success',
    'dialogue': 'primary',
    'emotion': 'warning',
    'action': 'danger',
    'psychology': 'info'
  }
  return typeMap[type] || 'info'
}

// 获取语料库类型文本
const getCorpusTypeText = (type) => {
  const textMap = {
    'description': '场景描述',
    'dialogue': '对话模板',
    'emotion': '情感表达',
    'action': '动作描写',
    'psychology': '心理描写'
  }
  return textMap[type] || type
}
</script>

<style scoped>
.panel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  overflow: hidden;
  height: calc(100vh - 150px);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.panel-icon {
  font-size: 18px;
}

.corpus-list {
  flex: 1;
  overflow-y: auto;
  padding-right: 4px;
  max-height: calc(100vh - 190px);
}

.corpus-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.corpus-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
  transform: translateY(-1px);
}

.corpus-content {
  flex: 1;
  text-align: left;
}

.corpus-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.corpus-header h4 {
  margin: 0;
  font-size: 14px;
  color: #303133;
}

.corpus-preview {
  margin: 8px 0;
  font-size: 13px;
  color: #606266;
}

.corpus-preview-truncated {
  cursor: help;
  transition: color 0.2s ease;
}

.corpus-preview-truncated:hover {
  color: #303133;
}

.corpus-actions {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
  margin-left: 8px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style>
