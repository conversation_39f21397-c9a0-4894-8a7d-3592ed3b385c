<template>
  <div class="panel-content">
    <div class="panel-header">
      <div class="panel-title">
        <span class="panel-icon">👥</span>
        <span>人物角色</span>
      </div>
      <div class="character-actions">
        <el-button size="small" type="primary" @click="addCharacter">
          <el-icon><Plus /></el-icon>
          新增
        </el-button>
        <el-button size="small" type="success" @click="showBatchGenerateDialog">
          🤖 AI批量生成
        </el-button>
      </div>
    </div>

    <div class="characters-list" v-loading="loading" element-loading-text="正在加载人物...">
      <div v-for="character in characters" :key="character.id" class="character-item">
        <div class="character-content" @click="editCharacter(character)">
          <div class="character-avatar">
            <img v-if="character.avatar" :src="character.avatar" />
            <div v-else class="default-avatar">{{ character.name?.charAt(0) || '？' }}</div>
          </div>
          <div class="character-info">
            <h4>{{ character.name }}</h4>
            <div class="character-meta">
              <el-tag :type="getRoleType(character.role)" size="small">{{ getRoleText(character.role) }}</el-tag>
              <el-tag v-if="character.gender" type="info" size="small">{{ getGenderText(character.gender) }}</el-tag>
              <span v-if="character.age" class="age-text">{{ character.age }}岁</span>
            </div>
            <el-tooltip
              v-if="character.personality"
              :content="character.personality"
              placement="right"
              :disabled="character.personality.length <= 60"
              effect="light"
              :show-after="300"
            >
              <p class="character-desc character-desc-truncated">
                {{ character.personality.length > 60 ? character.personality.substring(0, 60) + '...' : character.personality }}
              </p>
            </el-tooltip>
            <div class="character-tags" v-if="character.tags && character.tags.length">
              <el-tag v-for="tag in character.tags" :key="tag" size="small">{{ tag }}</el-tag>
            </div>
          </div>
        </div>
        <div class="character-actions">
          <el-dropdown @command="(cmd) => handleCharacterAction(cmd, character)" trigger="click">
            <el-button size="small" type="text" @click.stop>
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="edit">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-dropdown-item>
                <el-dropdown-item command="delete" divided>
                  <el-icon><Delete /></el-icon>
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <div v-if="characters.length === 0" class="empty-state">
        <p>暂无人物设定</p>
        <el-button size="small" @click="addCharacter">创建第一个角色</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import { Plus, MoreFilled, Edit, Delete } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  characters: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'add-character',
  'edit-character',
  'character-action',
  'batch-generate'
])

// 方法
const addCharacter = () => {
  emit('add-character')
}

const editCharacter = (character) => {
  emit('edit-character', character)
}

const handleCharacterAction = (command, character) => {
  emit('character-action', command, character)
}

const showBatchGenerateDialog = () => {
  emit('batch-generate')
}

// 获取角色类型样式
const getRoleType = (role) => {
  const roleMap = {
    'protagonist': 'danger',
    'supporting': 'primary',
    'antagonist': 'warning',
    'minor': 'info'
  }
  return roleMap[role] || 'info'
}

// 获取角色类型文本
const getRoleText = (role) => {
  const roleMap = {
    'protagonist': '主角',
    'supporting': '配角',
    'antagonist': '反派',
    'minor': '次要角色'
  }
  return roleMap[role] || '配角'
}

// 获取性别文本
const getGenderText = (gender) => {
  const genderMap = {
    'male': '男',
    'female': '女',
    'other': '其他'
  }
  return genderMap[gender] || '男'
}
</script>

<style scoped>
.panel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  overflow: hidden;
  height: calc(100vh - 150px);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.panel-icon {
  font-size: 18px;
}

.character-actions {
  display: flex;
  gap: 8px;
}

.characters-list {
  flex: 1;
  overflow-y: auto;
  padding-right: 4px;
  max-height: calc(100vh - 260px);
}

.character-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.character-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
  transform: translateY(-1px);
}

.character-content {
  display: flex;
  align-items: center;
  flex: 1;
  cursor: pointer;
}

.character-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10px;
  flex-shrink: 0;
}

.character-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-avatar {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
}

.character-info {
  flex: 1;
}

.character-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #303133;
}

.character-meta {
  display: flex;
  align-items: center;
  gap: 6px;
  margin: 4px 0;
  flex-wrap: wrap;
}

.character-meta .age-text {
  font-size: 12px;
  color: #909399;
  margin-left: 4px;
}

.character-desc {
  font-size: 12px;
  color: #666;
  margin: 4px 0;
  line-height: 1.3;
  max-height: 2.6em;
  overflow: hidden;
  text-overflow: ellipsis;
}

.character-desc-truncated {
  cursor: help;
  transition: color 0.2s ease;
}

.character-desc-truncated:hover {
  color: #303133;
}

.character-tags {
  margin-top: 4px;
}

.character-tags .el-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}

.character-item .character-actions {
  flex-shrink: 0;
  margin-left: 8px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
  background: white;
  border-radius: 8px;
}
</style>
